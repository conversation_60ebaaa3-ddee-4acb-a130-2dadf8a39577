/// Model representing a Mauritanian application in the Noujoum Store
class MauritanianApp {
  final String id;
  final String name;
  final String description;
  final String iconUrl;
  final List<String> screenshots;
  final String category;
  final String subcategory;
  final String targetAudience; // "Public", "Business", "Government"
  final String developerName;
  final String developerType; // "Individual", "Company"
  final String contactEmail;
  final String contactPhone;
  final String websiteUrl;
  final List<String> tags;
  final DateTime dateAdded;
  final double rating; // Static rating for display
  final int downloadCount; // Static download count for display
  final bool isFeatured;

  const <PERSON>uritanian<PERSON><PERSON>({
    required this.id,
    required this.name,
    required this.description,
    required this.iconUrl,
    required this.screenshots,
    required this.category,
    required this.subcategory,
    required this.targetAudience,
    required this.developerName,
    required this.developerType,
    required this.contactEmail,
    required this.contactPhone,
    required this.websiteUrl,
    required this.tags,
    required this.dateAdded,
    this.rating = 4.0,
    this.downloadCount = 1000,
    this.isFeatured = false,
  });

  /// Create a copy of this app with updated fields
  MauritanianApp copyWith({
    String? id,
    String? name,
    String? description,
    String? iconUrl,
    List<String>? screenshots,
    String? category,
    String? subcategory,
    String? targetAudience,
    String? developerName,
    String? developerType,
    String? contactEmail,
    String? contactPhone,
    String? websiteUrl,
    List<String>? tags,
    DateTime? dateAdded,
    double? rating,
    int? downloadCount,
    bool? isFeatured,
  }) {
    return MauritanianApp(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      screenshots: screenshots ?? this.screenshots,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      targetAudience: targetAudience ?? this.targetAudience,
      developerName: developerName ?? this.developerName,
      developerType: developerType ?? this.developerType,
      contactEmail: contactEmail ?? this.contactEmail,
      contactPhone: contactPhone ?? this.contactPhone,
      websiteUrl: websiteUrl ?? this.websiteUrl,
      tags: tags ?? this.tags,
      dateAdded: dateAdded ?? this.dateAdded,
      rating: rating ?? this.rating,
      downloadCount: downloadCount ?? this.downloadCount,
      isFeatured: isFeatured ?? this.isFeatured,
    );
  }

  /// Convert to JSON for potential future API integration
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'screenshots': screenshots,
      'category': category,
      'subcategory': subcategory,
      'targetAudience': targetAudience,
      'developerName': developerName,
      'developerType': developerType,
      'contactEmail': contactEmail,
      'contactPhone': contactPhone,
      'websiteUrl': websiteUrl,
      'tags': tags,
      'dateAdded': dateAdded.toIso8601String(),
      'rating': rating,
      'downloadCount': downloadCount,
      'isFeatured': isFeatured,
    };
  }

  /// Create from JSON for potential future API integration
  factory MauritanianApp.fromJson(Map<String, dynamic> json) {
    return MauritanianApp(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      iconUrl: json['iconUrl'],
      screenshots: List<String>.from(json['screenshots']),
      category: json['category'],
      subcategory: json['subcategory'],
      targetAudience: json['targetAudience'],
      developerName: json['developerName'],
      developerType: json['developerType'],
      contactEmail: json['contactEmail'],
      contactPhone: json['contactPhone'],
      websiteUrl: json['websiteUrl'],
      tags: List<String>.from(json['tags']),
      dateAdded: DateTime.parse(json['dateAdded']),
      rating: json['rating']?.toDouble() ?? 4.0,
      downloadCount: json['downloadCount'] ?? 1000,
      isFeatured: json['isFeatured'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MauritanianApp && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MauritanianApp(id: $id, name: $name, category: $category)';
  }
}
